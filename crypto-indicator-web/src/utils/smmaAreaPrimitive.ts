import type {
  ISeriesPrimitive,
  IPrimitivePaneView,
  IPrimitivePaneRenderer,
  SeriesAttachedParameter,
  CanvasRenderingTarget2D,
  Time,
} from 'lightweight-charts';
import type { CryptoCurrencyStatisticsDto } from '../generated';

interface SMMAAreaData {
  time: Time;
  smma15: number;
  smma29: number;
  color: 'gold' | 'blue' | 'gray';
}

interface SMMAAreaOptions {
  colors: {
    gold: string;
    blue: string;
    gray: string;
  };
}

class SMMAAreaRenderer implements IPrimitivePaneRenderer {
  private _data: SMMAAreaData[] = [];
  private _options: SMMAAreaOptions;

  constructor(data: SMMAAreaData[], options: SMMAAreaOptions) {
    this._data = data;
    this._options = options;
  }

  draw(target: CanvasRenderingTarget2D): void {
    const ctx = target.canvasRenderingContext2D;
    const pixelRatio = target.pixelRatio;

    if (this._data.length < 2) return;

    // Group consecutive data points by color
    const colorSegments = this._groupConsecutiveByColor();

    colorSegments.forEach(segment => {
      if (segment.data.length < 2) return;

      ctx.save();
      ctx.globalAlpha = 0.3;
      ctx.fillStyle = this._options.colors[segment.color];

      ctx.beginPath();

      // Draw the path for the upper line (SMMA-15 or SMMA-29, whichever is higher)
      let firstPoint = true;
      segment.data.forEach((point, index) => {
        const x = this._timeToX(point.time, target);
        const upperValue = Math.max(point.smma15, point.smma29);
        const y = this._priceToY(upperValue, target);

        if (x !== null && y !== null) {
          if (firstPoint) {
            ctx.moveTo(x * pixelRatio, y * pixelRatio);
            firstPoint = false;
          } else {
            ctx.lineTo(x * pixelRatio, y * pixelRatio);
          }
        }
      });

      // Draw the path for the lower line in reverse order
      for (let i = segment.data.length - 1; i >= 0; i--) {
        const point = segment.data[i];
        const x = this._timeToX(point.time, target);
        const lowerValue = Math.min(point.smma15, point.smma29);
        const y = this._priceToY(lowerValue, target);

        if (x !== null && y !== null) {
          ctx.lineTo(x * pixelRatio, y * pixelRatio);
        }
      }

      ctx.closePath();
      ctx.fill();
      ctx.restore();
    });
  }

  private _groupConsecutiveByColor(): Array<{ color: 'gold' | 'blue' | 'gray'; data: SMMAAreaData[] }> {
    const segments: Array<{ color: 'gold' | 'blue' | 'gray'; data: SMMAAreaData[] }> = [];
    let currentSegment: { color: 'gold' | 'blue' | 'gray'; data: SMMAAreaData[] } | null = null;

    this._data.forEach(point => {
      if (!currentSegment || currentSegment.color !== point.color) {
        if (currentSegment) {
          segments.push(currentSegment);
        }
        currentSegment = { color: point.color, data: [point] };
      } else {
        currentSegment.data.push(point);
      }
    });

    if (currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  }

  private _timeToX(time: Time, target: CanvasRenderingTarget2D): number | null {
    // Simple approximation - in a real implementation, you'd get this from the chart's time scale
    // For now, we'll use a basic linear mapping
    const timeNum = typeof time === 'string' ? new Date(time).getTime() : (time as number);
    const firstTime = typeof this._data[0]?.time === 'string' 
      ? new Date(this._data[0].time).getTime() 
      : (this._data[0]?.time as number) || 0;
    const lastTime = typeof this._data[this._data.length - 1]?.time === 'string'
      ? new Date(this._data[this._data.length - 1].time).getTime()
      : (this._data[this._data.length - 1]?.time as number) || 0;
    
    if (lastTime === firstTime) return 0;
    
    const progress = (timeNum - firstTime) / (lastTime - firstTime);
    return progress * target.mediaSize.width;
  }

  private _priceToY(price: number, target: CanvasRenderingTarget2D): number | null {
    // Simple approximation - in a real implementation, you'd get this from the chart's price scale
    const allPrices = this._data.flatMap(d => [d.smma15, d.smma29]);
    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    
    if (maxPrice === minPrice) return target.mediaSize.height / 2;
    
    const progress = (price - minPrice) / (maxPrice - minPrice);
    return target.mediaSize.height * (1 - progress); // Invert Y axis
  }
}

class SMMAAreaPaneView implements IPrimitivePaneView {
  private _renderer: SMMAAreaRenderer;

  constructor(data: SMMAAreaData[], options: SMMAAreaOptions) {
    this._renderer = new SMMAAreaRenderer(data, options);
  }

  renderer(): IPrimitivePaneRenderer {
    return this._renderer;
  }

  zOrder(): 'bottom' | 'normal' | 'top' {
    return 'bottom'; // Draw behind other series
  }

  update(data: SMMAAreaData[], options: SMMAAreaOptions): void {
    this._renderer = new SMMAAreaRenderer(data, options);
  }
}

export class SMMAAreaPrimitive implements ISeriesPrimitive {
  private _paneView: SMMAAreaPaneView;
  private _data: SMMAAreaData[] = [];
  private _options: SMMAAreaOptions = {
    colors: {
      gold: 'rgba(249, 226, 175, 0.4)',
      blue: 'rgba(137, 180, 250, 0.4)',
      gray: 'rgba(147, 153, 178, 0.3)',
    },
  };

  constructor() {
    this._paneView = new SMMAAreaPaneView(this._data, this._options);
  }

  updateAllViews(): void {
    this._paneView.update(this._data, this._options);
  }

  paneViews(): readonly IPrimitivePaneView[] {
    return [this._paneView];
  }

  attached(param: SeriesAttachedParameter): void {
    // Called when primitive is attached to a series
  }

  detached(): void {
    // Called when primitive is detached from a series
  }

  setData(cryptoData: CryptoCurrencyStatisticsDto): void {
    this._data = this._transformData(cryptoData);
    this.updateAllViews();
  }

  private _transformData(cryptoData: CryptoCurrencyStatisticsDto): SMMAAreaData[] {
    return cryptoData.indicatorValues
      .filter(item => 
        item.smma_15 !== undefined && 
        item.smma_15 !== null &&
        item.smma_29 !== undefined && 
        item.smma_29 !== null &&
        item.color && 
        ['gold', 'blue', 'gray'].includes(item.color)
      )
      .map(item => ({
        time: item.timestamp.split('T')[0] as Time,
        smma15: item.smma_15,
        smma29: item.smma_29,
        color: item.color as 'gold' | 'blue' | 'gray',
      }))
      .sort((a, b) => {
        const timeA = typeof a.time === 'string' ? new Date(a.time).getTime() : a.time;
        const timeB = typeof b.time === 'string' ? new Date(b.time).getTime() : b.time;
        return timeA - timeB;
      });
  }
}
