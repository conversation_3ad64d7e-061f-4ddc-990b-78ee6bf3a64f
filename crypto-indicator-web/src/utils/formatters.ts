export const formatters = {
  price: (value?: number, currency: string = "USD"): string => {
    if (value === undefined || value === null) return "-";

    return (
      (currency === "USD" ? "$" : " ") +
      value.toLocaleString("en-US", {
        style: "decimal",
        minimumFractionDigits: 2,
        maximumFractionDigits: currency === "BTC" ? 8 : 6,
      }) +
      (currency === "BTC" ? " BTC" : " ")
    );
  },

  marketCap: (value?: number): string => {
    if (!value || value === 0) return "-";

    const suffixes = [
      { threshold: 1e12, suffix: "T" },
      { threshold: 1e9, suffix: "B" },
      { threshold: 1e6, suffix: "M" },
      { threshold: 1e3, suffix: "K" },
    ];

    for (const { threshold, suffix } of suffixes) {
      if (value >= threshold) {
        return `$${(value / threshold).toLocaleString("en-US", {
          maximumFractionDigits: 2,
          minimumFractionDigits: 1,
        })}${suffix}`;
      }
    }

    return `${value.toLocaleString("en-US", { maximumFractionDigits: 0 })}`;
  },

  technicalIndicator: (value?: number): string => {
    if (value === undefined || value === null) return "-";

    return value.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  },

  percentage: (value?: number): string => {
    if (value === undefined || value === null) return "-";

    const sign = value >= 0 ? "+" : "";
    return `${sign}${value.toFixed(2)}%`;
  },

  volume: (value?: number): string => {
    if (!value || value === 0) return "-";

    const suffixes = [
      { threshold: 1e9, suffix: "B" },
      { threshold: 1e6, suffix: "M" },
      { threshold: 1e3, suffix: "K" },
    ];

    for (const { threshold, suffix } of suffixes) {
      if (value >= threshold) {
        return `${(value / threshold).toLocaleString("en-US", {
          maximumFractionDigits: 1,
        })}${suffix}`;
      }
    }

    return value.toLocaleString("en-US", { maximumFractionDigits: 0 });
  },
};
