import type { ChartData, CandlestickData } from '../types/chart';

export const dataTransformers = {
  /**
   * Transform indicator values to candlestick data format
   */
  transformCandlestickData: (indicatorValues: any[]): CandlestickData[] => {
    return indicatorValues
      .filter(item => item.open && item.high && item.low && item.close)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        open: item.open!,
        high: item.high!,
        low: item.low!,
        close: item.close!,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to SMMA line data format
   */
  transformSMMAData: (indicatorValues: any[], period: number): ChartData[] => {
    const fieldName = `smma_${period}`;

    return indicatorValues
      .filter(item => (item as any)[fieldName] !== undefined && (item as any)[fieldName] !== null)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        value: (item as any)[fieldName] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to baseline data for SMMA area visualization
   * Uses the higher SMMA value as the data point and lower SMMA as baseline
   */
  transformSMMABaselineData: (indicatorValues: any[], signalColor: 'gold' | 'blue' | 'gray'): ChartData[] => {
    return indicatorValues
      .filter(item =>
        item.smma_15 !== undefined &&
        item.smma_15 !== null &&
        item.smma_29 !== undefined &&
        item.smma_29 !== null &&
        item.color === signalColor
      )
      .map(item => ({
        time: item.timestamp.split('T')[0],
        value: Math.max(item.smma_15, item.smma_29), // Use the higher value as the line
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Calculate dynamic baseline value for SMMA area visualization
   */
  calculateSMMABaseline: (indicatorValues: any[], signalColor: 'gold' | 'blue' | 'gray'): number => {
    const filteredData = indicatorValues.filter(item =>
      item.smma_15 !== undefined &&
      item.smma_15 !== null &&
      item.smma_29 !== undefined &&
      item.smma_29 !== null &&
      item.color === signalColor
    );

    if (filteredData.length === 0) return 0;

    // Use the average of the lower SMMA values as baseline
    const lowerValues = filteredData.map(item => Math.min(item.smma_15, item.smma_29));
    return lowerValues.reduce((sum, val) => sum + val, 0) / lowerValues.length;
  },

  /**
   * Generic data transformer for any numeric field
   */
  transformIndicatorData: (
    indicatorValues: any[],
    field: string
  ): ChartData[] => {
    return indicatorValues
      .filter(item => (item as any)[field] !== undefined && (item as any)[field] !== null)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        value: (item as any)[field] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },
};
