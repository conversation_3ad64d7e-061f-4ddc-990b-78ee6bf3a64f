import { ColorType } from "lightweight-charts";

export const CHART_THEME = {
  layout: {
    background: { type: ColorType.Solid, color: "#181825" },
    textColor: "#cdd6f4",
  },
  grid: {
    vertLines: { color: "#313244" },
    horzLines: { color: "#313244" },
  },
  rightPriceScale: {
    borderColor: "#45475a",
  },
  crosshair: {
    vertLine: {
      color: "#94e2d5",
      labelBackgroundColor: "#45475a",
    },
    horzLine: {
      color: "#94e2d5",
      labelBackgroundColor: "#45475a",
    },
  },
} as const;

export const CHART_CONFIG = {
  dimensions: {
    width: 1200,
    height: 600,
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: false,
  },
  crosshair: {
    mode: 1,
  },
  zoom: {
    speed: 1 / 30, // Faster zoom speed (1/3 instead of default 1/8)
  },
} as const;

export const SERIES_CONFIG = {
  candlestick: {
    upColor: "#00ff88",
    downColor: "#ff4444",
    borderVisible: false,
    wickUpColor: "#00ff88",
    wickDownColor: "#ff4444",
  },
  smma: {
    15: { color: "#edff00", lineWidth: 2, priceScaleId: "indicators" },
    29: { color: "#0033ff", lineWidth: 2, priceScaleId: "indicators" },
  },
  smmaBaseline: {
    gold: {
      topLineColor: "rgba(249, 226, 175, 0.8)",
      topFillColor1: "rgba(249, 226, 175, 0.4)",
      topFillColor2: "rgba(249, 226, 175, 0.1)",
      bottomLineColor: "rgba(249, 226, 175, 0.8)",
      bottomFillColor1: "rgba(249, 226, 175, 0.4)",
      bottomFillColor2: "rgba(249, 226, 175, 0.1)",
      priceScaleId: "indicators",
    },
    blue: {
      topLineColor: "rgba(137, 180, 250, 0.8)",
      topFillColor1: "rgba(137, 180, 250, 0.4)",
      topFillColor2: "rgba(137, 180, 250, 0.1)",
      bottomLineColor: "rgba(137, 180, 250, 0.8)",
      bottomFillColor1: "rgba(137, 180, 250, 0.4)",
      bottomFillColor2: "rgba(137, 180, 250, 0.1)",
      priceScaleId: "indicators",
    },
    gray: {
      topLineColor: "rgba(147, 153, 178, 0.8)",
      topFillColor1: "rgba(147, 153, 178, 0.3)",
      topFillColor2: "rgba(147, 153, 178, 0.1)",
      bottomLineColor: "rgba(147, 153, 178, 0.8)",
      bottomFillColor1: "rgba(147, 153, 178, 0.3)",
      bottomFillColor2: "rgba(147, 153, 178, 0.1)",
      priceScaleId: "indicators",
    },
  },
} as const;

// Helper functions to get chart configurations
export const getChartConfig = () => ({
  ...CHART_CONFIG,
  ...CHART_THEME,
});

export const getSeriesConfig = () => SERIES_CONFIG;
