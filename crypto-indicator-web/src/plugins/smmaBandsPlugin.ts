import type {
  ISeriesPrimitive,
  IPrimitivePaneView,
  IPrimitivePaneRenderer,
  SeriesAttachedParameter,
  CanvasRenderingTarget2D,
  Time,
  PriceToCoordinateConverter,
  TimeToCoordinateConverter,
} from 'lightweight-charts';

interface SMMAABandsData {
  time: Time;
  smma15: number;
  smma29: number;
  color: 'gold' | 'blue' | 'gray';
}

interface SMMAABandsOptions {
  colors: {
    gold: string;
    blue: string;
    gray: string;
  };
}

class SMMAABandsRenderer implements IPrimitivePaneRenderer {
  private _data: SMMAABandsData[];
  private _options: SMMAABandsOptions;

  constructor(data: SMMAABandsData[], options: SMMAABandsOptions) {
    this._data = data;
    this._options = options;
  }

  draw(
    target: CanvasRenderingTarget2D,
    priceToCoordinate: PriceToCoordinateConverter,
    timeToCoordinate: TimeToCoordinateConverter
  ): void {
    const ctx = target.canvasRenderingContext2D;
    const pixelRatio = target.pixelRatio;

    if (this._data.length < 2) return;

    // Group consecutive data points by color
    const colorSegments = this._groupConsecutiveByColor();

    colorSegments.forEach(segment => {
      if (segment.data.length < 2) return;

      ctx.save();
      ctx.globalAlpha = 0.3;
      ctx.fillStyle = this._options.colors[segment.color];

      ctx.beginPath();

      // Draw the upper line (higher SMMA value)
      let firstPoint = true;
      segment.data.forEach(point => {
        const x = timeToCoordinate(point.time);
        const upperValue = Math.max(point.smma15, point.smma29);
        const y = priceToCoordinate(upperValue);

        if (x !== null && y !== null) {
          if (firstPoint) {
            ctx.moveTo(x * pixelRatio, y * pixelRatio);
            firstPoint = false;
          } else {
            ctx.lineTo(x * pixelRatio, y * pixelRatio);
          }
        }
      });

      // Draw the lower line in reverse order to close the path
      for (let i = segment.data.length - 1; i >= 0; i--) {
        const point = segment.data[i];
        const x = timeToCoordinate(point.time);
        const lowerValue = Math.min(point.smma15, point.smma29);
        const y = priceToCoordinate(lowerValue);

        if (x !== null && y !== null) {
          ctx.lineTo(x * pixelRatio, y * pixelRatio);
        }
      }

      ctx.closePath();
      ctx.fill();
      ctx.restore();
    });
  }

  private _groupConsecutiveByColor(): Array<{ color: 'gold' | 'blue' | 'gray'; data: SMMAABandsData[] }> {
    const segments: Array<{ color: 'gold' | 'blue' | 'gray'; data: SMMAABandsData[] }> = [];
    let currentSegment: { color: 'gold' | 'blue' | 'gray'; data: SMMAABandsData[] } | null = null;

    this._data.forEach(point => {
      if (!currentSegment || currentSegment.color !== point.color) {
        if (currentSegment) {
          segments.push(currentSegment);
        }
        currentSegment = { color: point.color, data: [point] };
      } else {
        currentSegment.data.push(point);
      }
    });

    if (currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  }
}

class SMMAABandsPaneView implements IPrimitivePaneView {
  private _renderer: SMMAABandsRenderer;

  constructor(data: SMMAABandsData[], options: SMMAABandsOptions) {
    this._renderer = new SMMAABandsRenderer(data, options);
  }

  renderer(): IPrimitivePaneRenderer {
    return this._renderer;
  }

  zOrder(): 'bottom' {
    return 'bottom'; // Draw behind other series
  }

  update(data: SMMAABandsData[], options: SMMAABandsOptions): void {
    this._renderer = new SMMAABandsRenderer(data, options);
  }
}

export class SMMAABandsPrimitive implements ISeriesPrimitive {
  private _paneView: SMMAABandsPaneView;
  private _data: SMMAABandsData[] = [];
  private _options: SMMAABandsOptions = {
    colors: {
      gold: 'rgba(249, 226, 175, 0.4)',
      blue: 'rgba(137, 180, 250, 0.4)',
      gray: 'rgba(147, 153, 178, 0.3)',
    },
  };
  private _requestUpdate?: () => void;

  constructor(options?: Partial<SMMAABandsOptions>) {
    if (options) {
      this._options = { ...this._options, ...options };
    }
    this._paneView = new SMMAABandsPaneView(this._data, this._options);
  }

  updateAllViews(): void {
    this._paneView.update(this._data, this._options);
  }

  paneViews(): readonly IPrimitivePaneView[] {
    return [this._paneView];
  }

  attached(param: SeriesAttachedParameter): void {
    this._requestUpdate = param.requestUpdate;
  }

  detached(): void {
    this._requestUpdate = undefined;
  }

  setData(data: SMMAABandsData[]): void {
    this._data = data;
    this.updateAllViews();
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  updateOptions(options: Partial<SMMAABandsOptions>): void {
    this._options = { ...this._options, ...options };
    this.updateAllViews();
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }
}
